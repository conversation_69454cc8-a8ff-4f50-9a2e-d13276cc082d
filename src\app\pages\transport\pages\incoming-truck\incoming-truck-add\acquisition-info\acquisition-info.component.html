<div class="acquisition-info">
  <form>
    <section [formGroup]="incomingTruckExpense">
      <div class="row m-t-10">
        <div class="col-lg-3 col-md-6 col-12">
          <label>PO/RO#</label>
          <input class="form-control" type="text" placeholder="Enter PO/RO#" formControlName="poRoNumber" />
          <app-error-messages [control]="incomingTruckExpense.controls.poRoNumber"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label>Invoice#</label>
          <input class="form-control" type="text" placeholder="Enter invoice#" formControlName="invoiceNumber" />
          <app-error-messages [control]="incomingTruckExpense.controls.invoiceNumber"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Acquisition Cost</label>
          <input class="form-control" type="number" placeholder="Enter amount" formControlName="amount" />
          <app-error-messages [control]="incomingTruckExpense.controls.amount"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Acquisition Method</label>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="acquisitionType"
            formControlName="acquisitionMethodId"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select acquisition method"
          >
            <ng-template pTemplate="empty">
              <ng-container
                [ngTemplateOutlet]="emptyMessage"
                [ngTemplateOutletContext]="{ loader: loaders.acquisitionType, data: acquisitionType }"
              ></ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span>{{ item.name }}</span>
            </ng-template>
          </p-dropdown>
          <app-error-messages [control]="incomingTruckExpense.controls.acquisitionMethodId"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Select Contact/Vendor/Supplier</label>
          <ng-container *appHasPermission="[permissionActions.CREATE_VENDORS]">
            <button
              class="btn btn-primary add-btn"
              id="addVendorBtn"
              type="button"
              [appImageIconSrc]="constants.staticImages.icons.add"
              (click)="showDialog()"
              *ngIf="!isViewMode"
            ></button>
          </ng-container>
          <p-dialog
            header="You can create either Vendor or Supplier or Contact from here."
            [(visible)]="displaySelectionDialog"
            [modal]="true"
            [style]="{ width: '45vw' }"
            [draggable]="false"
            [resizable]="false"
          >
            <ng-template pTemplate="footer">
              <p-button label="Vendor" styleClass="p-button-text" (click)="openAndCloseModel(ModelType.VENDOR)"></p-button>
              <p-button label="Supplier" styleClass="p-button-text" (click)="openAndCloseModel(ModelType.SUPPLIER)"></p-button>
              <p-button label="Contact" styleClass="p-button-text" (click)="openAndCloseModel(ModelType.CONTACT)"></p-button>
            </ng-template>
          </p-dialog>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="vendors"
            formControlName="vendorId"
            (onChange)="setContactVendorSupplier($event)"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            [virtualScroll]="true"
            [itemSize]="30"
            placeholder="Select vendor"
            [optionDisabled]="isOptionDisabled.bind(this)"
          >
            <ng-template pTemplate="empty">
              <ng-container
                [ngTemplateOutlet]="emptyMessage"
                [ngTemplateOutletContext]="{ loader: loaders.vendors, data: vendors }"
              ></ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span [ngClass]="{'disabled-dropdown-item': item?.archived}">
                {{ item?.name }}
                <span *ngIf="item?.archived">{{ constants.archived }}</span>
              </span>
            </ng-template>
          </p-dropdown>
          <app-error-messages [control]="incomingTruckExpense.controls.vendorId"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Purchased By</label>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="users"
            formControlName="purchasedById"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="stockNumber"
            [virtualScroll]="true"
            [itemSize]="30"
            placeholder="Select purchased by"
          >
            <ng-template pTemplate="empty">
              <ng-container
                [ngTemplateOutlet]="emptyMessage"
                [ngTemplateOutletContext]="{ loader: loaders.users, data: users }"
              ></ng-container>
            </ng-template>
            <ng-template let-pipelineOwner pTemplate="item">
              <span>{{ pipelineOwner.name }}</span>
            </ng-template>
          </p-dropdown>
          <app-error-messages [control]="incomingTruckExpense.controls.purchasedById"></app-error-messages>
        </div>
      </div>
      <div class="row m-t-5">
        <div class="col-lg-6 col-12 position-relative d-flex flex-column">
          <label class="m-t-30">Notes</label>
          <textarea placeholder="Enter notes" rows="6" formControlName="description"></textarea>
        </div>
        <div *ngIf="showAttachmentsTab" class="col-lg-6 col-12">
          <div class="d-flex justify-content-between document-upload">
            <div>
              <label class="m-t-20">Document</label>
            </div>
            <div class="d-flex justify-content-end" *ngIf="!isViewMode">
              <p-message severity="info" text="Max file size allowed is 10MB" styleClass="p-mr-2 file-size-message m-t-10"></p-message>
            </div>
          </div>
          <div>
            <section
              *ngIf="!isViewMode"
              class="m-t-10"
              [ngClass]="{
                'w-100': !fileUploadProgresses.length && !(incomingTruckDetails?.incomingTruckExpenseDTO?.expensesAttachmentDTO && incomingTruckDetails.incomingTruckExpenseDTO.expensesAttachmentDTO.length > 0)
              }"
            >
              <div
                [ngClass]="{
                  files: fileUploadProgresses.length || (incomingTruckDetails?.incomingTruckExpenseDTO?.expensesAttachmentDTO && incomingTruckDetails.incomingTruckExpenseDTO.expensesAttachmentDTO.length > 0)
                }"
                class="drop-zone"
              >
                <span class="drop-zone__prompt">
                  <em class="pi pi-upload"></em>
                  <p class="title">Drop file to attach</p>
                  <p class="subtitle">or click to browse</p>
                </span>
                <input
                  type="file"
                  name="myFile"
                  class="drop-zone__input cursor-pointer"
                  #inputElement
                  multiple
                  [accept]="constants.allowedImgAndOdfFormats"
                  (change)="onFileSelect($event)"
                />
              </div>
            </section>
          </div>
          <div>
            <div *ngFor="let fileProgress of fileUploadProgresses; let fileIndex = index" class="files">
              <div class="file-box-wrapper">
                <div
                  class="file-box"
                  [ngClass]="{ 'in-progress': !fileProgress?.isResolved }"
                  [style.background-image]="'url(' + fileProgress.fileProperty?.fileUrl + ')'"
                ></div>
              </div>
              <div class="d-flex justify-content-between m-t-10">
                <div>
                  <fa-icon class="text-danger" [icon]="faIcons.faFilePdf"></fa-icon>
                  <span class="file-name text-truncate m-l-10">{{ fileProgress?.file?.name }}</span>
                </div>
                <div class="d-flex justify-content-end">
                  <a (click)="utils.viewUploadedPdf(acquisitionFile)">
                    <em class="pi pi-eye download-icon cursor-pointer"></em>
                  </a>
                  <a (click)="utils.downloadUploadedPdf(acquisitionFile)">
                    <em class="pi pi-download download-icon cursor-pointer ms-2"></em>
                  </a>
                  <em class="pi pi-trash text-danger ms-2 cursor-pointer" (click)="removeFileFromUpload(fileIndex)"></em>
                </div>
              </div>
            </div>
          </div>
          <div class="isEditMode">
            <div *ngFor="let attachment of incomingTruckDetails?.incomingTruckExpenseDTO?.expensesAttachmentDTO; let attachmentIndex = index" class="files">
              <div class="file-box-wrapper">
                <div
                  class="file-box"
                  [style.background-image]="
                    'url(' + attachment?.fullExpensesAttachmentUrl + ')'
                  "
                ></div>
              </div>
              <div class="d-flex justify-content-between m-t-10">
                <div>
                  <fa-icon class="text-danger" [icon]="faIcons.faFilePdf"></fa-icon>
                  <span class="file-name text-truncate m-l-10">{{
                    getFileName(attachment?.url)
                  }}</span>
                </div>
                <div class="d-flex justify-content-end">
                  <a
                    [href]="attachment?.fullExpensesAttachmentUrl"
                    target="_blank"
                  >
                    <em class="pi pi-eye download-icon cursor-pointer"></em>
                  </a>
                  <em
                    class="pi pi-download download-icon ms-2 cursor-pointer"
                    (click)="downloadImage(attachment)"
                  ></em>
                  <em
                    *ngIf="!isViewMode"
                    class="pi pi-trash text-danger ms-2 cursor-pointer"
                    appShowLoaderOnApiCall
                    (click)="onDelete(attachment?.id, $event, attachmentIndex)"
                  ></em>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section [formGroup]="generalInfoFormGroup">
      <div class="row">
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Category</label>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="categoryTypes"
            formControlName="unitTypeCategoryId"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select category type"
            (onChange)="changeCategory($event.value)"
          >
            <ng-template pTemplate="empty">
              <ng-container
                [ngTemplateOutlet]="emptyMessage"
                [ngTemplateOutletContext]="{ loader: loaders.categoryType, data: categoryTypes }"
              ></ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span>{{ item.name }}</span>
            </ng-template>
          </p-dropdown>
          <app-error-messages [control]="generalInfoFormGroup.controls.unitTypeCategoryId"> </app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">VIN</label>
          <input class="form-control" type="text" placeholder="Enter vin" formControlName="vin" />
          <app-error-messages [control]="generalInfoFormGroup.controls.vin"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Unit Type</label>
          <ng-container *appHasPermission="[permissionActions.CREATE_UNIT_TYPE]">
            <button
              class="btn btn-primary add-btn"
              id="addUnitTypeBtn"
              type="button"
              *ngIf="!isViewMode"
              [appImageIconSrc]="constants.staticImages.icons.add"
              (click)="openModel(ModelType.UNIT_TYPE)"
            ></button>
          </ng-container>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="unitTypes"
            formControlName="unitTypeId"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select unit type"
          >
            <ng-template pTemplate="empty">
              <ng-container
                [ngTemplateOutlet]="emptyMessage"
                [ngTemplateOutletContext]="{ loader: loaders.unitTypes, data: unitTypes }"
              ></ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span>{{ item.name }}</span>
            </ng-template>
          </p-dropdown>
          <app-error-messages [control]="generalInfoFormGroup.controls.unitTypeId"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Year</label>
          <input class="form-control" type="number" placeholder="Enter year" formControlName="year" />
          <app-error-messages [control]="generalInfoFormGroup.controls.year"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Make</label>
          <ng-container *appHasPermission="[permissionActions.CREATE_MAKE_MODEL]">
            <button
              class="btn btn-primary add-btn"
              id="addMakeBtn"
              type="button"
              *ngIf="!isViewMode"
              [appImageIconSrc]="constants.staticImages.icons.add"
              (click)="openModel(ModelType.MAKE)"
            ></button>
          </ng-container>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="makes"
            formControlName="makeId"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select make"
            (onChange)="changeMake($event.value)"
          >
            <ng-template pTemplate="empty">
              <ng-container
                [ngTemplateOutlet]="emptyMessage"
                [ngTemplateOutletContext]="{ loader: loaders.makes, data: makes }"
              ></ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span>{{ item.name }}</span>
            </ng-template>
          </p-dropdown>
          <app-error-messages [control]="generalInfoFormGroup.controls.makeId"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Model</label>
          <ng-container *appHasPermission="[permissionActions.CREATE_MAKE_MODEL]">
            <button
              class="btn btn-primary add-btn"
              id="addModelBtn"
              type="button"
              *ngIf="!isViewMode"
              [appImageIconSrc]="constants.staticImages.icons.add"
              (click)="openModel(ModelType.MODEL)"
            ></button>
          </ng-container>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="models"
            formControlName="unitModelId"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select model"
          >
            <ng-template pTemplate="empty">
              <ng-container
                [ngTemplateOutlet]="emptyMessage"
                [ngTemplateOutletContext]="{ loader: loaders.models, data: models }"
              ></ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span>{{ item.name }}</span>
            </ng-template>
          </p-dropdown>
          <app-error-messages [control]="generalInfoFormGroup.controls.unitModelId"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Owned By</label>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="dealerOptions"
            formControlName="ownerId"
            (onChange)="setLotLocation($event.value)"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select owner"
          >
            <ng-template pTemplate="empty">
              <ng-container
                [ngTemplateOutlet]="emptyMessage"
                [ngTemplateOutletContext]="{ loader: loaders.ownedBy, data: dealerOptions }"
              ></ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span>{{ item.name }}</span>
            </ng-template>
          </p-dropdown>
          <app-error-messages [control]="generalInfoFormGroup.controls.ownerId"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Designation</label>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="designations"
            formControlName="designationId"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select designation"
          >
            <ng-template pTemplate="empty">
              <ng-container
                [ngTemplateOutlet]="emptyMessage"
                [ngTemplateOutletContext]="{ loader: loaders.designations, data: designations }"
              ></ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span>{{ item.name }}</span>
            </ng-template>
          </p-dropdown>
          <app-error-messages [control]="generalInfoFormGroup.controls.designationId"></app-error-messages>
        </div>
      </div>
    </section>
    <section [formGroup]="incomingTruckExpense">
      <div class="row align-items-center m-t-5">
        <div class="col-lg-3 col-md-6 col-12">
          <label
            ><span class="required">Select assignee</span>
            <span class="ms-2 text-primary text-decoration-underline cursor-pointer" (click)="assignToMe()">Assign to me</span>
          </label>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="users"
            formControlName="assigneeId"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select assignee"
          >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage"
              [ngTemplateOutletContext]="{ loader: loaders.users, data: users }"></ng-container>
          </ng-template>
          </p-dropdown>
          <app-error-messages [control]="incomingTruckExpense.controls.assigneeId"></app-error-messages>
        </div>
        <div class="col-md-6 col-12">
          <div class="field-checkbox m-t-24">
            <div class="d-flex">
              <p-checkbox
                [binary]="true"
                inputId="acquisitionCheckbox"
                formControlName="enable"
                [(ngModel)]="enableGeneralTab"
                (ngModelChange)="onCheckedChangeAcquisition()"
              ></p-checkbox>
              <label for="acquisitionCheckbox"><span class="ms-2 cursor-pointer">Check to fill Payment processing information</span></label>
            </div>
          </div>
        </div>
      </div>
    </section>
  </form>
</div>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateVendor"
  position="right"
  (onHide)="modelPopups.showCreateVendor = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-vendor-add (onClose)="onAddEditPopupClose(ModelType.VENDOR)" *ngIf="modelPopups.showCreateVendor"> </app-vendor-add>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateSupplier"
  position="right"
  (onHide)="modelPopups.showCreateSupplier = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-supplier-add (onClose)="onAddEditPopupClose(ModelType.SUPPLIER)" *ngIf="modelPopups.showCreateSupplier"> </app-supplier-add>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateContact"
  position="right"
  (onHide)="modelPopups.showCreateContact = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-contact-customer-add (onClose)="onAddEditPopupClose(ModelType.CONTACT)" *ngIf="modelPopups.showCreateContact">
  </app-crm-contact-customer-add>
</p-sidebar>
<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateMake"
  position="right"
  (onHide)="modelPopups.showCreateMake = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-add-new-make (onClose)="onAddEditPopupClose(ModelType.MAKE)" *ngIf="modelPopups.showCreateMake" [categoryId]="categoryId">
  </app-add-new-make>
</p-sidebar>
<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateModel"
  position="right"
  (onHide)="modelPopups.showCreateModel = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-add-new-model (onClose)="onAddEditPopupClose(ModelType.MODEL)" [makeId]="selectedMakeId" *ngIf="modelPopups.showCreateModel">
  </app-add-new-model>
</p-sidebar>
<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateUnitType"
  position="right"
  (onHide)="modelPopups.showCreateUnitType = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-add-new-unit-type
    (onClose)="onAddEditPopupClose(ModelType.UNIT_TYPE)"
    [unitTypeCategoryId]="categoryId"
    *ngIf="modelPopups.showCreateUnitType"
  >
  </app-add-new-unit-type>
</p-sidebar>
