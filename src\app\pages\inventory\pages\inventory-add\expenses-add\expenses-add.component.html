<div class="modal-title">
  <h4>{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>

<form [formGroup]="expensesFormGroup" (ngSubmit)="onSubmit()" [ngClass]="{ loading: isLoading }" class="expense-add">
  <div class="content" *ngIf="!isLoading; else loaderTemplate">
    <section>
      <div class="row">
        <div class="col-lg-6 col-md-6 col-12">
          <label>PO/RO#</label>
          <input class="form-control" type="text" placeholder="Enter PO/RO#" formControlName="poRoNumber" />
          <app-error-messages [control]="expensesFormGroup.controls?.poRoNumber"></app-error-messages>
        </div>
        <div class="col-lg-6 col-md-6 col-12">
          <label>Invoice#</label>
          <input class="form-control" type="text" placeholder="Enter invoice#" formControlName="invoiceNumber" />
          <app-error-messages [control]="expensesFormGroup.controls?.invoiceNumber"></app-error-messages>
        </div>
      </div>
      <div class="row m-t-5">
        <div class="col-12 position-relative">
          <label>Description</label>
          <textarea placeholder="Enter description" rows="3" formControlName="description"></textarea>
          <app-error-messages [control]="expensesFormGroup.controls?.description"></app-error-messages>
        </div>
        <div class="col-12">
          <label>Vendor/Supplier</label>
          <ng-container *appHasPermission="[permissionActions.CREATE_VENDORS, permissionActions.CREATE_SUPPLIERS]">
            <button class="btn btn-primary add-btn" id="addVendorBtn" type="button" [appImageIconSrc]="constants.staticImages.icons.add" (click)="showDialog()"></button>
          </ng-container>
          <p-dialog
            header="You can create either Vendor or Supplier from here."
            [(visible)]="displaySelectionDialog"
            [modal]="true"
            [style]="{ width: '45vw' }"
            [draggable]="false"
            [resizable]="false"
          >
            <ng-template pTemplate="footer">
              <p-button label="Vendor" styleClass="p-button-text" (click)="openModel(ModelType.VENDOR)"></p-button>
              <p-button label="Supplier" styleClass="p-button-text" (click)="openModel(ModelType.SUPPLIER)"></p-button>
            </ng-template>
          </p-dialog>
          <p-dropdown
            appendTo="body"
            [options]="vendorSupplierList"
            formControlName="vendorId"
            (onChange)="setVendorSupplier($event)"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select vendor/supplier"
          >
            <ng-template pTemplate="empty">
              <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.vendors, data: vendorSupplierList }"></ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span>{{ item.name }}</span>
            </ng-template>
          </p-dropdown>
          <app-error-messages [control]="expensesFormGroup.controls.vendorId"></app-error-messages>
        </div>
      </div>
      <div class="row m-t-5">
        <div class="col-lg-6 col-md-6 col-12">
          <label class="required">Amount</label>
          <input class="form-control" type="number" placeholder="Enter amount" formControlName="amount" appNumberOnly/>
          <app-error-messages [control]="expensesFormGroup.controls?.amount"></app-error-messages>
        </div>
        <div class="col-lg-6 col-md-6 col-12">
          <label class="required">Date of Expense</label>
          <div>
            <p-calendar appendTo="body" formControlName="transDate" [showIcon]="true" [showButtonBar]="true" [readonlyInput]="true" inputId="dateIcon"> </p-calendar>
          </div>
          <app-error-messages [control]="expensesFormGroup.controls?.transDate"></app-error-messages>
        </div>
      </div>
      <div class="row m-t-5">
        <div class="col-12">
          <label class="required">Expense Type</label>
          <p-dropdown
            appendTo="body"
            [options]="expensesType"
            formControlName="expenseTypeId"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select expenses type"
          >
            <ng-template pTemplate="empty">
              <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.expensesType, data: expensesType }"> </ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span>{{ item.name }}</span>
            </ng-template>
          </p-dropdown>
          <app-error-messages [control]="expensesFormGroup.controls?.expenseTypeId"></app-error-messages>
        </div>
      </div>
      <div class="row m-t-5">
        <div class="col-12">
          <div class="d-flex space-between">
            <div>
              <label>Document</label>
            </div>
            <div class="d-flex justify-content-end">
              <p-message severity="info" text="Max file size allowed is 10MB" styleClass="p-mr-2 file-size-message"> </p-message>
            </div>
          </div>
          <section class="m-t-10" [ngClass]="{ 'w-100': !fileUploadProgresses?.length && !expensesInfo?.expensesAttachments?.length }">
            <div [ngClass]="{ files: fileUploadProgresses?.length || expensesInfo?.expensesAttachments?.length }" class="drop-zone">
              <span class="drop-zone__prompt">
                <em class="pi pi-upload"></em>
                <p class="title">Drop file to attach</p>
                <p class="subtitle">or click to browse</p>
              </span>
              <input type="file" name="myFile" class="drop-zone__input cursor-pointer" #inputElement (change)="onFileSelect($event)" [disabled]="isViewMode" [accept]="constants.allowedPdfFormats" multiple/>
            </div>
          </section>
          <div>
            <div *ngFor="let fileProgress of fileUploadProgresses; let fileIndex = index" class="files">
              <div class="file-box-wrapper">
                <div class="file-box" [ngClass]="{ 'in-progress': !fileProgress?.isResolved }" [style.background-image]="'url(' + fileProgress.fileProperty?.fileUrl + ')'"></div>
              </div>
              <div class="d-flex space-between m-t-10">
                <div>
                  <fa-icon class="text-danger" [icon]="faIcons.faFilePdf"></fa-icon>
                  <span class="file-name text-truncate m-l-10">{{ fileProgress?.file?.name }}</span>
                </div>
                <div class="d-flex justify-content-end">
                  <em class="pi pi-trash text-danger ms-2 cursor-pointer" (click)="removeFileFromUpload(fileIndex)"></em>
                </div>
              </div>
            </div>
          </div>
          <div class="isEditMode">
            <div *ngFor="let file of expensesInfo?.expensesAttachments; let fileIndex = index" class="files">
              <div class="file-box-wrapper">
                <div class="file-box" [style.background-image]="'url(' + file?.fullExpensesAttachmentUrl + ')'"></div>
              </div>
              <div class="d-flex space-between m-t-10">
                <div>
                  <fa-icon class="text-danger" [icon]="faIcons.faFilePdf"></fa-icon>
                  <span class="file-name text-truncate m-l-10">{{ getFileName(file.url) }}</span>
                </div>
                <div class="d-flex justify-content-end">
                  <em class="pi pi-download download-icon" (click)="downloadImage(file)"></em>
                  <em class="pi pi-trash text-danger ms-2 cursor-pointer" appShowLoaderOnApiCall (click)="onDelete(file.id, $event)" *ngIf="!isViewMode"></em>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall>Save</button>
    <button class="btn btn-primary" type="button" (click)="onSubmitAndAddNew()" *ngIf="!isEditMode" appShowLoaderOnApiCall>Save & Add New</button>
  </div>
</form>

<ng-template #loaderTemplate>
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
</ng-template>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateVendor"
  position="right"
  (onHide)="modelPopups.showCreateVendor = false"
  [fullScreen]="true"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-vendor-add (onClose)="onAddEditPopupClose($event)" *ngIf="modelPopups.showCreateVendor"> </app-vendor-add>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateSupplier"
  position="right"
  (onHide)="modelPopups.showCreateSupplier = false"
  [fullScreen]="true"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-supplier-add (onClose)="onAddEditPopupClose($event)" *ngIf="modelPopups.showCreateSupplier"> </app-supplier-add>
</p-sidebar>
