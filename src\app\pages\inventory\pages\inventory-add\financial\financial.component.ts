import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Constants, MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { allowExtensions, getRefactorFileName } from '@core/utils/fileUpaloadName.util';
import { VendorListItem } from '@pages/administration/models';
import { ContactDetails } from '@pages/administration/models/crm.model';
import { AuthService } from '@pages/auth/services/auth.service';
import { CrmService } from '@pages/crm/services/crm.service';
import { ActualExpenses, AssociationsUnits, ExpensesAttachment, ExpensesListFilter, ExpensesListItem, FinancialCreateParam, FinancialInformation, InventoryListItem, SalesPersonDetails, financialTitle } from '@pages/inventory/models';
import { ExpensesService } from '@pages/inventory/services/expenses.service';
import { FinancialService } from '@pages/inventory/services/financial.service';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { SoldTruckService } from '@pages/pipeline/pages/sold-truck-board/sold-truck.service';
import { IncomingTruckBoardListItem, ModelType } from '@pages/transport/models/incoming-truck.model';
import * as saveAs from 'file-saver';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { ConfirmationService } from 'primeng/api';
import { DropdownChangeEvent } from 'primeng/dropdown';
import { takeUntil } from 'rxjs';
import { FileProperties, FileUploadProgress, IdNameModel } from 'src/app/@shared/models';
import { CommonService } from 'src/app/@shared/services/common.service';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';

@Component({
  selector: 'app-financial',
  templateUrl: './financial.component.html',
  styleUrls: ['./financial.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush

})
export class FinancialComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() filterParams: ExpensesListFilter = new ExpensesListFilter();
  @Input() salePrice!: number;
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() isViewMode!: boolean;
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;
  @Input() isPrimaryInventory!: boolean;
  @Input() activeAssociatedUnit!: AssociationsUnits;
  @Input() financialInformation!: FinancialInformation;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() onFincancialSubmit: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() expensessPopupClosed = new EventEmitter<void>();
  financialFormGroup!: FormGroup;
  ModelType = ModelType;
  expenses: ExpensesListItem[] = [];
  purchasingAgent: IdNameModel[] = [];
  selectedExpenses!: ExpensesListItem | null;
  hasDataBeenModified = false;
  showCreateModal = false;
  accordionTabs = {
    pricing: true,
    acquisitionInfo: true,
    unitExpenses: true,
    warranties: true,
    internetOptions: true
  };

  loaders = {
    acquisitionMethod: false,
    purchasingAgent: false,
    expensesType: false,
    vendors: false,
    purchaseBy: false,
    previousOwnerName: false
  };
  designations: IdNameModel[] = [];
  actualExpenses!: ActualExpenses;
  finalActualExpenses = 0.00;
  projectedRetailProfit!: number;
  totalProjectedInvestment!: number;
  taxAmount!: number;
  projectedWholeSaleProfit!: number;
  totalActualInvestment!: number;
  decimalNumber = '0.00';
  financialTitle = financialTitle;
  unitId!: number | undefined;
  expensesType: IdNameModel[] = [];
  vendorList: IdNameModel[] = [];
  contactsVendors: IdNameModel[] = [];
  purchaseByList: SalesPersonDetails[] = [];
  fileUploadProgresses: FileUploadProgress[] = [];
  taskFileUploadPath = 'Expenses Files';
  showCreateVendor = false;
  isEditMode = false;
  showAttachmentsTab = true;
  initialInvestment = 0.00;
  displaySelectionDialog!: boolean;
  modelPopups = {
    showCreateVendor: false,
    showCreateSupplier: false,
    showCreateContact: false,
    showCreateModel: false,
    showCreateMake: false,
    showCreateModal: false
  };
  contactList: ContactDetails[] = [];
  selectedVendor!: VendorListItem | null;
  acquisitionFile!: File;
  unitIdList: number[] = [];
  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly authService: AuthService,
    private readonly crmService: CrmService,
    private readonly fileUploadService: FileUploadService,
    private readonly soldTruckService: SoldTruckService,
    private readonly commonService: CommonService,
    private readonly expensesService: ExpensesService,
    private readonly financialService: FinancialService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly inventoryService: InventoryService
  ) {
    super();
    this.paginationConfig.itemsPerPageOptions = [5, 10, 25, 50, 100];
    this.paginationConfig.itemsPerPage = 5;
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.addFinancialInfo();
    this.getPurchasingAgentList();
    this.getExpensesTypeList();
    this.getVendorList();
    this.getPurchaseByName();
    this.getCurrentUser();
    this.inventoryService.contactFinancialLoaded.asObservable().subscribe({
      next: (response: boolean) => {
        if (response) {
          this.getVendorList();
        }
      }
    })
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!this.isViewMode && this.isPrimaryInventory) {
      this.financialFormGroup?.controls['retailAskingPrice']?.enable();
      this.financialFormGroup?.controls['wholeSalePrice']?.enable();
      this.financialFormGroup?.controls['acquisitionMethodId']?.enable();
      this.expensesFormGroup?.enable();
    }
    this.finalActualExpenses = this.financialInformation?.actualExpenses ? this.financialInformation?.actualExpenses : 0.00;
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  addFinancialInfo(): void {
    if (this.inventoryIncomingInfo) {
      this.unitId = this.inventoryIncomingInfo.unitId;
    }
    else {
      this.unitId = this.inventoryInfo?.id;
    }
    this.financialFormGroup.patchValue({
      acquisitionCost: this.financialInformation?.acquisitionCost,
      acquisitionDate: this.financialInformation?.acquisitionDate ? new Date(this.financialInformation?.acquisitionDate) : '',
      purchasingAgentName: this.financialInformation?.purchasingAgent?.name
    })
    this.expensesFormGroup.patchValue({
      unitId: this.financialInformation?.unitId,
      financialId: this.financialInformation?.id
    })

    if (
      !this.utils.hasSubModulePermission(
        this.authService.getRoleInfo().privilegeActionResponseDTOs,
        [
          this.permissionActions.CREATE_FINANCIAL,
          this.permissionActions.UPDATE_FINANCIAL,
          this.permissionActions.DELETE_EXPENSES
        ])
    ) {
      this.financialFormGroup.disable();
      this.expensesFormGroup.disable();
    }

    this.setPipelineConfigInFormGroup();
    this.isLoading = false;
    this.cdf.detectChanges();
    this.getAll();
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user && this.expensesFormGroup) {
        this.currentUser = user;
      }
    });
  }

  getAll(): void {
    this.isLoading = true;
    if (this.inventoryIncomingInfo) {
      this.filterParams.unitId = this.inventoryIncomingInfo?.unitId;
    }
    else {
      this.filterParams.unitId = this.activeAssociatedUnit?.id ? this.activeAssociatedUnit?.id : this.inventoryInfo?.generalInformation.id;
    }
    this.filterParams.orderBy = this.orderBy;
    if (this.filterParams.unitId) {

      this.expensesService.getListWithFiltersWithPagination<ExpensesListFilter, ExpensesListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.expenses.expensesList)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (res) => {
            this.expenses = res.content;
            this.setPaginationParamsFromPageResponse<ExpensesListItem>(res);
            this.isLoading = false;
            this.cdf.detectChanges();
          },
          error: () => {
            this.isLoading = false;
            this.cdf.detectChanges();
          }
        });
    }
  }

  private initializeFormGroup(): void {
    this.financialFormGroup = this.formBuilder.group({
      retailAskingPrice: new FormControl(0.00),
      wholeSalePrice: new FormControl(0.00),
      netProfitAmount: new FormControl(0),
      expenses: this.newExpensesFormGroup,
    });
    if (this.isViewMode) {
      this.financialFormGroup.disable();
    }
  }

  get expensesFormGroup(): FormGroup {
    return this.financialFormGroup?.get('expenses') as FormGroup;
  }

  get newExpensesFormGroup(): FormGroup {
    return this.formBuilder.group({
      id: new FormControl(null),
      poRoNumber: new FormControl(null),
      invoiceNumber: new FormControl(null),
      description: new FormControl(null),
      amount: new FormControl(null, [Validators.required]),
      transDate: new FormControl(null, [Validators.required]),
      acquisitionMethodId: new FormControl(null, [Validators.required]),
      financialId: new FormControl(this.financialInformation?.id),
      firstExpense: new FormControl(true),
      unitId: new FormControl(this.financialInformation?.unitId),
      expensesAttachments: this.expensesAttachments,
      purchasedById: new FormControl(null, [Validators.required]),
      vendorId: new FormControl(null, [Validators.required]),
      contactAndVendorAndSupplierType: new FormControl(null),
      crmContactId: new FormControl(null),
      supplierId: new FormControl(null),
    });

  }

  get expensesAttachments(): ExpensesAttachment[] {
    let expensesAttachments: ExpensesAttachment[] = this.fileUploadProgresses.map(fileProgress => ({ url: fileProgress.uploadedFileUrl }));
    if (this.financialInformation?.expense?.expensesAttachments?.length) {
      expensesAttachments = [...this.financialInformation?.expense?.expensesAttachments, ...expensesAttachments];
    }
    return expensesAttachments;
  }

  get financialCreateParams(): FinancialCreateParam {
    this.setVendorContactSupplierToExpense()
    const totalProjectedInvestment = this.getTotalProjectedInvestment(this.initialInvestment, this.financialInformation?.actualExpenses);
    return {
      ...this.financialFormGroup.value,
      expenses: { ...this.expensesFormGroup.value, id: this.financialInformation?.expense?.id, financialId: this.financialInformation.id, unitId: this.financialInformation.unitId, firstExpense: true },
      unitId: this.financialInformation?.unitId,
      id: this.financialInformation?.id,
      actualExpenses: this.financialInformation?.actualExpenses,
      purchasingAgentId: this.expensesFormGroup.value.purchasedById,
      totalProjectedInvestment: totalProjectedInvestment ? Number(totalProjectedInvestment) : null,
      netProfitAmount: this.getProfitAmount,
    };
  }

  private get getProfitAmount(): number {
    let profit = 0;
    if (this.salePrice) {
      profit = this.salePrice - Number(this.getTotalProjectedInvestment(this.initialInvestment, this.financialInformation.actualExpenses));
    } else {
      profit = this.financialFormGroup.value.retailAskingPrice - Number(this.getTotalProjectedInvestment(this.initialInvestment, this.financialInformation.actualExpenses));
    }
    return profit ?? 0;
  }

  setVendorContactSupplierToExpense() {
    const type = this.expensesFormGroup.controls['contactAndVendorAndSupplierType'].value;
    const id = Number(this.expensesFormGroup.controls['vendorId'].value?.toString()?.split('-')[0])

    const resetPreviousOwner = () => {
      this.expensesFormGroup.controls['vendorId'].setValue(null);
      this.expensesFormGroup.controls['crmContactId'].setValue(null);
      this.expensesFormGroup.controls['supplierId'].setValue(null);
    };
    resetPreviousOwner();

    switch (type) {
      case ModelType.CONTACT.toUpperCase():
        this.expensesFormGroup.controls['crmContactId'].setValue(id);
        break;
      case ModelType.VENDOR.toUpperCase():
        this.expensesFormGroup.controls['vendorId'].setValue(id);
        break;
      case ModelType.SUPPLIER.toUpperCase():
        this.expensesFormGroup.controls['supplierId'].setValue(id);
        break;
      default:
        break;
    }
  }

  onAddEditPopupClose(modelType: string): void {
    this.selectedExpenses = null;
    this.modelPopups.showCreateModal = false;
    this.modelPopups.showCreateMake = false;
    this.modelPopups.showCreateVendor = false;
    this.modelPopups.showCreateContact = false;
    this.modelPopups.showCreateSupplier = false;
    this.displaySelectionDialog = false;
    this.expensessPopupClosed.emit();
    switch (modelType) {
      case ModelType.VENDOR:
      case ModelType.SUPPLIER:
        this.getVendorList();
        break;
      case ModelType.CONTACT:
        this.getContactInfo();
        this.getVendorList();
        break;
      case ModelType.EXPENSES:
        this.getAll();
        break;
      default:
        break;
    }
    this.inventoryService.setContactGeneralLoaded(true);
  }
  private getContactInfo(): void {
    this.loaders.previousOwnerName = true;
    this.crmService.getList<ContactDetails>(API_URL_UTIL.admin.crm.contact).pipe(takeUntil(this.destroy$)).subscribe({
      next: (contactList) => {
        this.contactList = contactList;
        this.loaders.previousOwnerName = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.previousOwnerName = false;
        this.cdf.detectChanges();
      }
    });
  }

  getFinancial(): void {
    const endpoint = this.financialInformation.id.toString();
    this.financialService.get<FinancialInformation>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (financialInformation) => {
        this.financialInformation = financialInformation;
        this.financialFormGroup.patchValue({
          acquisitionCost: this.financialInformation?.acquisitionCost,
          acquisitionDate: this.financialInformation?.acquisitionDate ? new Date(`${this.financialInformation?.acquisitionDate}`) : '',
          purchasingAgentName: this.financialInformation?.purchasingAgent?.name
        })
        this.financialFormGroup.controls['acquisitionCost']?.disable();
        this.financialFormGroup.controls['acquisitionDate']?.disable();
        this.financialFormGroup.controls['purchasingAgentName']?.disable();
      }
    });
  }

  private getPurchasingAgentList(): void {
    this.loaders.purchasingAgent = true;
    this.financialService.getPurchasingAgent().pipe(takeUntil(this.destroy$)).subscribe({
      next: (purchasingAgent) => {
        this.purchasingAgent = purchasingAgent;
        this.loaders.purchasingAgent = false;
      },
      error: () => {
        this.loaders.purchasingAgent = false;
      }
    });
  }

  onSubmit(close = true): void {
    if (this.financialFormGroup.invalid) {
      this.financialFormGroup.markAllAsTouched();
      return;
    }
    this.editFinancial();
  }

  onSubmitAndNext(): void {
    this.onSubmit(false);
  }

  editFinancial(): void {
    this.financialService.update(this.financialCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.financialUpdateSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
    });
  }

  onEdit(expenses: ExpensesListItem): void {
    this.modelPopups.showCreateModal = true;
    this.selectedExpenses = expenses;
  }

  onDelete(expenses: ExpensesListItem, event: Event): void {
    this.confirmationService.confirm({
      header: "Confirmation",
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'expenses'),
      icon: icons.triangle,
      accept: () => {
        this.onDeleteConfirmation(expenses);
      }
    });
  }

  onDeleteConfirmation(expenses: ExpensesListItem): void {
    this.expensesService.delete(expenses.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.expensesDeleteSuccess);
          this.expensessPopupClosed.emit();
          this.getAll();
          this.getFinancial();
        }
      });
  }

  getTotalProjectedInvestment(acquisitionCost: number, actualExpenses: number) {
    this.totalProjectedInvestment = (acquisitionCost + actualExpenses);
    return (Number(this.totalProjectedInvestment ? this.totalProjectedInvestment : 0)).toFixed(2);
  }

  getProjectedRetailProfit(retailPrice: number, acquisitionCost: number, actualExpenses: number) {
    this.projectedRetailProfit = retailPrice - Number(this.getTotalProjectedInvestment(acquisitionCost, actualExpenses));
    return Number((retailPrice - Number(this.getTotalProjectedInvestment(acquisitionCost, actualExpenses))).toFixed(2));
  }

  getWholesalePriceProfit(wholeSalePrice: number, acquisitionCost: number, actualExpenses: number) {
    this.projectedWholeSaleProfit = wholeSalePrice - Number(this.getTotalProjectedInvestment(acquisitionCost, actualExpenses));
    return Number((wholeSalePrice - Number(this.getTotalProjectedInvestment(acquisitionCost, actualExpenses))).toFixed(2));
  }

  private async setPipelineConfigInFormGroup(): Promise<void> {
    if (this.financialInformation) {
      this.financialFormGroup.patchValue({
        retailAskingPrice: this.financialInformation?.retailAskingPrice || this.decimalNumber,
        wholeSalePrice: this.financialInformation?.wholeSalePrice || this.decimalNumber,
      });
      this.setExpensesFormGroup(this.financialInformation);
      this.cdf.detectChanges();
    }
  }

  setExpensesFormGroup(inventoryDetails: FinancialInformation) {
    setTimeout(() => {
      this.financialFormGroup.patchValue({
        netProfitAmount: inventoryDetails?.netProfitAmount,
        expenses: {
          id: inventoryDetails?.expense?.id,
          financialId: inventoryDetails?.expense?.financialId,
          firstExpense: inventoryDetails?.expense?.firstExpense,
          poRoNumber: inventoryDetails?.expense?.poRoNumber,
          invoiceNumber: inventoryDetails?.expense?.invoiceNumber,
          description: inventoryDetails?.expense?.description,
          amount: inventoryDetails?.expense?.amount,
          transDate: inventoryDetails?.expense?.transDate ? new Date(`${this.financialInformation?.expense?.transDate}`) : '',
          acquisitionMethodId: inventoryDetails?.acquisitionMethod?.id,
          unitId: inventoryDetails?.expense?.unitId,
          expensesAttachments: this.expensesAttachments,
          purchasedById: inventoryDetails?.expense?.purchasingAgent?.id,
          contactAndVendorAndSupplierType: inventoryDetails?.expense?.contactAndVendorAndSupplierType ?? this.inventoryInfo?.previousOwner?.type,
          vendorId: this.patchPreviousOwner(inventoryDetails),
          crmContactId: this.patchPreviousOwner(inventoryDetails),
          supplierId: this.patchPreviousOwner(inventoryDetails),
        }
      })
    }, 2000)
    setTimeout(() => {
      this.cdf.detectChanges()

    }, 1000)
    this.initialInvestment = inventoryDetails?.expense?.amount ? inventoryDetails?.expense?.amount : 0.00
  }

  private patchPreviousOwner(inventoryDetails: FinancialInformation): string {
    const { expense } = inventoryDetails;
    const { previousOwner } = this.inventoryInfo || {};
    if (expense?.contactAndVendorAndSupplierType) {
      const type = expense.contactAndVendorAndSupplierType.toUpperCase();
      const id = expense.crmContact?.id || expense.vendor?.id || expense.supplier?.id;
      return id ? `${id.toString()}-${type}` : '';
    } else if (previousOwner?.type) {
      const type = previousOwner.type.toUpperCase();
      const id = previousOwner.previousOwnerContactId || previousOwner.previousOwnerVendorId || previousOwner.previousOwnerSupplierId;
      return id ? `${id.toString()}-${type}` : '';
    }
    return '';
  }


  getFileName(fileUrl: string | undefined): string {
    if (!fileUrl) {
      return '';
    }
    return fileUrl.split('_').slice(1).join('_');
  }


  selectValue(value: string, label: string): void {
    if (value === this.decimalNumber && label === financialTitle.retailAskingPrice) {
      this.financialFormGroup.controls['retailAskingPrice'].setValue(null);
    }
    else if (value === this.decimalNumber && label === financialTitle.wholeSalePrice) {
      this.financialFormGroup.controls['wholeSalePrice'].setValue(null);
    }
  }

  private getExpensesTypeList(): void {
    this.loaders.expensesType = true;
    this.expensesService.getAcquisitionList().pipe(takeUntil(this.destroy$)).subscribe({
      next: (purchasingAgent) => {
        this.expensesType = purchasingAgent;
        this.loaders.expensesType = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.expensesType = false;
      }
    });
  }
  private getVendorList(): void {
    this.loaders.vendors = true;
    this.commonService.getList(API_URL_UTIL.vendorsContactsSuppliers).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: any) => {
        if (typeof (res[0]?.id) === 'number') {
          res.map((d: any) => {
            d.name = `${d.name} (${d.type})`;
            d.id = `${d.id}-${d.type}`;
            return d
          })
        }
        this.contactsVendors = res;
        this.loaders.vendors = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.vendors = false;
      }
    });
  }

  openVenderModel() {
    this.showCreateVendor = true;
  }


  private getPurchaseByName(): void {
    this.loaders.purchaseBy = true;
    this.userAnnotationService.get(API_URL_UTIL.userAnnotation.userFilter).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: SalesPersonDetails[]) => {
        this.purchaseByList = res;
        this.loaders.purchaseBy = false;
      },
      error: () => {
        this.loaders.purchaseBy = false;
      }
    });
  }


  onFileSelect(event: any) {
    if (event.target?.files?.length) {
      for (const file of event.target.files) {
        if (file.size > this.constants.fileSize) {
          this.toasterService.warning(MESSAGES.fileUploadMessage)
          return
        }
        const modifiedFileName = getRefactorFileName(file.name);
        const isExtensionSupported = allowExtensions(modifiedFileName, Constants.allowedPdfFormats)
        if (isExtensionSupported) {
          const modifiedFile = new File([file], modifiedFileName, { type: file.type });
          this.uploadImage(modifiedFile);
        } else {
          this.toasterService.error(MESSAGES.fileTypeSupportedPDF);
          return
        }
      }
    }
  }

  uploadImage(file: File): void {
    const formData: FormData = new FormData();
    formData.append('multipartFile', file, `${file.name}`);
    const uploadProgress = new FileUploadProgress();
    uploadProgress.index = this.fileUploadProgresses.length;
    uploadProgress.file = file;
    this.fileUploadProgresses.push(uploadProgress);
    this.fileUploadService.setFileUploads(this.fileUploadProgresses);
    this.setFileUrl(file, uploadProgress);
    this.fileUploadService.uploadFile(formData, uploadProgress.index, `${this.currentUser?.id}`, this.taskFileUploadPath, this.fileUploadProgresses)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (fileUrl: { url: string }) => {
          this.acquisitionFile = file;
          uploadProgress.uploadedFileUrl = fileUrl.url;
          this.fileUploadService.setFileUploads(this.fileUploadProgresses);
          this.toasterService.success(MESSAGES.fileUploadSuccess);
          this.expensesFormGroup.patchValue({
            expensesAttachments: this.expensesAttachments
          })
        },
        error: err => {
          this.toasterService.error(MESSAGES.fileUploadError);
        }
      })
  }

  downloadServerPDF(file: ExpensesAttachment | null): void {
    if (file?.url) {
      const fileName = this.getFileName(file?.url);
      this.fileUploadService.downloadFile(file.url, fileName ?? 'file').pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/pdf" }), fileName);
        }
      });
    }
  }

  setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  deleteImageFromCloud(imageUrl: string, fileIndex: number, callbackFn: Function): void {
    this.fileUploadService.deleteFile([imageUrl]).pipe(takeUntil(this.destroy$)).subscribe(() => {
      callbackFn(fileIndex);
    })
  }

  removeFileFromUpload(fileIndex: number): void {
    const spliceImageFunction = () => {
      this.fileUploadProgresses.splice(fileIndex, 1);
      this.expensesFormGroup.patchValue({
        expensesAttachments: this.expensesAttachments
      });
      this.cdf.detectChanges();
    }
    const fileProgress = this.fileUploadProgresses[fileIndex];
    if (fileProgress.uploadedFileUrl) {
      this.deleteImageFromCloud(fileProgress.uploadedFileUrl, fileIndex, spliceImageFunction);
    }
  }

  onDeleteImage(attachmentId: number | undefined, event: Event): void {
    if (!attachmentId) {
      return;
    }
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      header: 'Confirmation',
      message: MESSAGES.deleteWarning.replace('{record}', 'attachment'),
      icon: icons.triangle,
      accept: () => {
        this.onDeleteConfirmationImage(attachmentId);
      }
    });
  }

  onDeleteConfirmationImage(attachmentId: number): void {
    this.expensesService.deleteExpenseAttachment(attachmentId).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.attachmentDeleteSuccess);
      const list = this.financialInformation?.expense?.expensesAttachments ?? [];
      const idx  = list.findIndex(a => a.id === attachmentId);
      if (idx > -1) list.splice(idx, 1);

      this.expensesFormGroup.patchValue({
        expensesAttachments: this.expensesAttachments
      });
      this.cdf.detectChanges();
    });
  }

  getAmount(value: number) {
    this.getTotalUnitAmount(value);
    this.initialInvestment = value;
  }

  showDialog() {
    this.displaySelectionDialog = true;
  }
  openAndCloseModel(type: string) {
    switch (type) {
      case ModelType.VENDOR:
        this.modelPopups.showCreateVendor = true;
        break;
      case ModelType.SUPPLIER:
        this.modelPopups.showCreateSupplier = true;
        break;
      case ModelType.CONTACT:
        this.modelPopups.showCreateContact = true;
        break;
      case ModelType.EXPENSES:
        this.modelPopups.showCreateModal = true;
        break;
      default:
        break;
    }
  }
  setContactVendorSupplier(event: DropdownChangeEvent) {
    const value = event.value.split('-')[1]
    this.expensesFormGroup.controls['contactAndVendorAndSupplierType'].setValue(value)
  }

  getTotalUnitAmount(initialValue: number) {
    setTimeout(() => {
      this.financialInformation.grandTotal = this.financialInformation.grandTotal - this.financialInformation.acquisitionCost + initialValue;
      this.financialInformation.acquisitionCost = initialValue;
      this.cdf.detectChanges();
    }, 0);
  }

  isOptionDisabled(option: ContactDetails): boolean {
    const selectedValue = this.expensesFormGroup.get('vendorId')?.value;
    return option.archived && option.id !== selectedValue;
  }
}
