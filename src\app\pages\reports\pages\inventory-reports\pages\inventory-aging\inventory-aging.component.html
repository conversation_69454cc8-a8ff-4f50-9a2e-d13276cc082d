<app-page-header [pageTitle]="pageTitle">
  <div class="top-header" headerActionBtn>
    <button class="btn btn-primary me-3" type="button" (click)="showListModal = true">
      <span>Config</span>
    </button>
    <button
      class="btn btn-primary left me-3 show-label"
      type="button"
      [appImageIconSrc]="constants.staticImages.icons.exportFile"
      (click)="exportUsersToExcel()"
      [disabled]="!paginationConfig.totalElements"
    >
      <span class="show-label">Export</span>
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-3 mr--12" *ngIf="isExporting"></fa-icon>
    </button>
  </div>
</app-page-header>
<div class="card tabs stock-truck-list">
  <form [formGroup]="dateFilterFormGroup" class="date-range-form pt-3 px-4" (ngSubmit)="onSubmit()">
    <div class="content d-flex flex-wrap justify-content-between">
      <div class="d-flex flex-wrap align-items-center">
        <div formGroupName="startDateGroup">
          <label>From</label>
          <p-calendar
            appendTo="body"
            formControlName="value"
            placeholder="mm/dd/yyyy"
            [showIcon]="true"
            [showButtonBar]="true"
            [readonlyInput]="true"
            inputId="startDateIcon"
            [maxDate]="dailySalesEndDateFormGroup.controls?.value?.value"
          ></p-calendar>
        </div>
        <div formGroupName="endDateGroup" class="ms-sm-3 ms-0">
          <label>To</label>
          <p-calendar
            appendTo="body"
            formControlName="value"
            placeholder="mm/dd/yyyy"
            [showIcon]="true"
            [showButtonBar]="true"
            [readonlyInput]="true"
            inputId="endDateIcon"
            [minDate]="dailySalesStartDateFormGroup.controls?.value?.value"
          ></p-calendar>
        </div>
        <div class="mt-4">
          <button class="btn btn-primary ms-md-4 ms-0">Go</button>
          <button type="button" class="btn btn-primary btn-sm ms-2" (click)="clearSearchInput()">Reset filters</button>
        </div>
      </div>
    </div>
  </form>
  <div class="tab-content">
    <p-table
      [columns]="selectedColumns"
      styleClass="p-datatable-gridlines"
      [value]="inventoryAgingList"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="'id'"
      [rowHover]="true"
      [loading]="isLoading"
      [resizableColumns]="true"
      columnResizeMode="expand"
      class="has-date-range-filter"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <ng-container *ngFor="let col of columns">
            <th *ngIf="col?.shorting" pResizableColumn pReorderableColumn [pSortableColumn]="col?.shortingKey" scope="col">
              {{ col.name }} <p-sortIcon [field]="col?.shortingKey"></p-sortIcon>
            </th>
            <th *ngIf="!col?.shorting" pResizableColumn pReorderableColumn scope="col">{{ col.name }}</th>
          </ng-container>
        </tr>
        <tr class="report-search-tr">
          <ng-container *ngFor="let col of columns">
            <th pResizableColumn scope="col">
              <span class="search-input" *ngIf="col.type === 'STRING'">
                <input pInputText placeholder="Search {{ col.name }}" type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
              </span>
              <span class="search-input" *ngIf="col.type === 'DOUBLE'">
                <input
                  pInputText
                  placeholder="Search {{ col.name }}"
                  type="number"
                  class="form-control"
                  (input)="tableSearchByColumn($event.target, col)"
                  [(ngModel)]="col.value"
                />
              </span>
              <span class="search-input" *ngIf="col.type === 'DATE'"> </span>
              <span class="search-input" *ngIf="col.type === 'MULTI_DROP_DOWN'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="salesRepList"
                  defaultLabel="Select a {{ col.name }}"
                  optionLabel="name"
                  optionValue="id"
                  [maxSelectedLabels]="1"
                  [(ngModel)]="accountRepIds"
                  selectedItemsLabel="{0} items selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  appendTo="body"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.salesRep, data: salesRepList }"></ng-container>
                  </ng-template>
                </p-multiSelect>
                <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="col.value?.length" (click)="clearSalesRep(col)"></fa-icon>
              </span>
              <span class="search-input" *ngIf="col.type === 'DROP_DOWN' && col.searchKey === 'financial.acquisitionMethod.id'">
                <p-dropdown
                  appPreventClearFilter
                  [options]="acquisitionType"
                  (onChange)="tableSearchByColumn($event, col)"
                  [(ngModel)]="acquisitionMethodId"
                  optionLabel="name"
                  optionValue="id"
                  [filter]="true"
                  filterBy="name"
                  [showClear]="true"
                  appendTo="body"
                  placeholder="Select {{ col.name }}"
                  [virtualScroll]="true"
                  [itemSize]="30"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.acquisitionType, data: acquisitionType }"></ng-container>
                  </ng-template>
                </p-dropdown>
              </span>
              <span class="search-input" *ngIf="col.type === 'DROP_DOWN' && col.name === 'Contact/Vendor/Supplier'">
                <p-dropdown
                  appPreventClearFilter
                  [options]="vendors"
                  (onChange)="handleVendorSearch($event)"
                  [(ngModel)]="vendorId"
                  optionLabel="name"
                  optionValue="id"
                  [filter]="true"
                  filterBy="name"
                  [showClear]="true"
                  appendTo="body"
                  placeholder="Select {{ col.name }}"
                  [virtualScroll]="true"
                  [itemSize]="30"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.vendors, data: vendors }"></ng-container>
                  </ng-template>
                </p-dropdown>
              </span>
            </th>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-ageData let-columns="columns" let-rowData let-rowIndex="rowIndex">
        <tr>
          <ng-container *ngFor="let col of columns">
            <td>
              <span *ngIf="col.type !== 'DATE' && col.type !== 'DOUBLE' && col.name !== 'Contact/Vendor/Supplier' && col.key !== 'inventoryAge'">
                {{ getEvaluatedExpression(col.key, ageData) }}
              </span>
              <span *ngIf="col.type !== 'DATE' && col.name !== 'Contact/Vendor/Supplier' && col.key === 'inventoryAge'">
                <span *ngIf="getEvaluatedExpression(col.key, ageData)" class="inventory-age" [ngClass]="getClass(ageData)">
                  {{ getEvaluatedExpression(col.key, ageData) }}
                </span>
              </span>
              <span *ngIf="col.type === 'DATE'">
                {{ getEvaluatedExpression(col.key, ageData) | date : constants.dateFormat }}
              </span>
              <span *ngIf="col.type === 'DOUBLE'">
                {{ getEvaluatedExpression(col.key, ageData) | currency: 'USD' : 'symbol': '1.2-2' }}
              </span>
              <span *ngIf="col.type !== 'DATE' && col.name === 'Contact/Vendor/Supplier'"> {{ getContactVendorSupplier(ageData) }}</span>
            </td>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="dropDownColumnList.length + 2" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
    <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
  </div>
</div>
<ng-template #emptyMessage let-loader="loader" let-data="data">
  <div class="d-flex justify-content-center">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
    <p *ngIf="!loader && !data?.length">No records found</p>
  </div>
</ng-template>
<p-sidebar
  [(visible)]="showListModal"
  position="right"
  (onHide)="showListModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-config-list (onClose)="showListModal = false"></app-config-list>
</p-sidebar>
