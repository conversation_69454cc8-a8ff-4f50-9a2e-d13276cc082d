<form [formGroup]="financialFormGroup" class="financial-parent-form">
  <div class="">
    <div class="row justify-content-end">
      <div class="col-auto right-side-block">
        <div class="inner-box d-flex justify-content-around" *ngIf="isPrimaryInventory">
          <div class="text3">Total Unit Investment</div>
          <div class="price-3 amount-style">${{ financialInformation?.grandTotal | number: '1.2-2' }}</div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-lg-4 col-12">
        <div class="row">
          <div class="title">
            <h4>PRICING</h4>
          </div>
          <div class="col-lg-6 col-md-6 col-12 m-t-0">
            <label>Retail Asking Price</label>
            <input
              class="form-control"
              type="number"
              step="0.01"
              formControlName="retailAskingPrice"
              id="addShopBtn"
              (click)="selectValue(financialFormGroup.controls.retailAskingPrice.value, financialTitle.retailAskingPrice)"
            />
            <app-error-messages [control]="financialFormGroup.controls.retailAskingPrice"></app-error-messages>
            <div class="d-flex justify-content-end">
              <span
                >PP&nbsp;<fa-icon class="icon" [icon]="faIcons.faInfoCircle"> </fa-icon>&nbsp;:&nbsp;
                <span
                  class="f-s-13"
                  [ngClass]="{
                    red: getProjectedRetailProfit(financialFormGroup.controls.retailAskingPrice.value, initialInvestment, finalActualExpenses) < 0,
                    green: getProjectedRetailProfit(financialFormGroup.controls.retailAskingPrice.value, initialInvestment, finalActualExpenses) > 0
                  }"
                >
                  ${{ getProjectedRetailProfit(financialFormGroup.controls.retailAskingPrice.value, initialInvestment, finalActualExpenses) | number: '1.2-2' }}</span
                >
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-8 col-12">
        <div class="title">
          <h4>OVERVIEW</h4>
        </div>
        <div class="row">
          <div class="col-lg-3 col-6 flex-col">
            <div class="text1">Acquisition Cost</div>
            <div class="text1">Actual Expenses</div>
          </div>
          <div class="col-lg-3 col-6 price">
            <div class="text2 amount-style">${{ initialInvestment | number: '1.2-2' }}</div>
            <div class="text2 amount-style">${{ finalActualExpenses | number: '1.2-2' }}</div>
          </div>
          <div class="col-lg-6 col-12">
            <div class="inner-box">
              <div class="text3">Total Projected Investment</div>
              <div class="price-3 amount-style">{{ getTotalProjectedInvestment(initialInvestment, finalActualExpenses) | currency: 'USD':'symbol':'1.2-2' }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 col-12">
          <section formGroupName="expenses">
            <div class="title">
              <h4>ACQUISITION INFORMATION</h4>
            </div>
            <div class="row">
              <div class="col-lg-3 col-md-6 col-12">
                <label>PO/RO#</label>
                <input class="form-control" type="text" placeholder="Enter PO/RO#" formControlName="poRoNumber" />
                <app-error-messages [control]="expensesFormGroup.controls.poRoNumber"></app-error-messages>
              </div>
              <div class="col-lg-3 col-md-6 col-12">
                <label>Invoice#</label>
                <input class="form-control" type="text" placeholder="Enter invoice#" formControlName="invoiceNumber" />
                <app-error-messages [control]="expensesFormGroup.controls.invoiceNumber"></app-error-messages>
              </div>
              <div class="col-lg-3 col-md-6 col-12">
                <label class="required">Acquisition Cost</label>
                <input class="form-control" type="number" placeholder="Enter acquisition cost" formControlName="amount" (keyup)="getAmount(expensesFormGroup.controls.amount.value)" />
                <app-error-messages [control]="expensesFormGroup.controls.amount"></app-error-messages>
              </div>
              <div class="col-lg-3 col-md-6 col-12">
                <label class="required">Purchase Date</label>
                <div>
                  <p-calendar appendTo="body" formControlName="transDate" [showIcon]="true" [showButtonBar]="true" [readonlyInput]="true" inputId="dateIcon"> </p-calendar>
                </div>
                <app-error-messages [control]="expensesFormGroup.controls.transDate"></app-error-messages>
              </div>
              <div class="col-lg-3 col-md-6 col-12">
                <label class="required">Acquistion Method</label>

                <p-dropdown
                  appPreventClearFilter
                  appendTo="body"
                  [options]="expensesType"
                  formControlName="acquisitionMethodId"
                  optionLabel="name"
                  [showClear]="true"
                  optionValue="id"
                  [filter]="true"
                  filterBy="name"
                  placeholder="Select acquistion method"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.expensesType, data: expensesType }"></ng-container>
                  </ng-template>
                  <ng-template let-item pTemplate="item">
                    <span>{{ item.name }}</span>
                  </ng-template>
                </p-dropdown>
                <app-error-messages [control]="expensesFormGroup.controls.acquisitionMethodId"></app-error-messages>
              </div>
              <div class="col-lg-3 col-md-6 col-12">
                <label class="required">Select Contact/Vendor/Supplier</label>
                <ng-container *ngIf="!isViewMode && isPrimaryInventory">
                  <button
                    class="btn btn-primary add-btn"
                    id="addVendorBtn"
                    type="button"
                    *appHasPermission="[permissionActions.CREATE_VENDORS]"
                    [appImageIconSrc]="constants.staticImages.icons.add"
                    (click)="showDialog()"
                  ></button>
                </ng-container>
                <p-dialog
                  header="You can create either Vendor or Contact from here."
                  [(visible)]="displaySelectionDialog"
                  [modal]="true"
                  [style]="{ width: '45vw' }"
                  [draggable]="false"
                  [resizable]="false"
                >
                  <ng-template pTemplate="footer">
                    <p-button label="Vendor" styleClass="p-button-text" (click)="openAndCloseModel(ModelType.VENDOR)"></p-button>
                    <p-button label="Supplier" styleClass="p-button-text" (click)="openAndCloseModel(ModelType.SUPPLIER)"></p-button>
                    <p-button label="Contact" styleClass="p-button-text" (click)="openAndCloseModel(ModelType.CONTACT)"></p-button>
                  </ng-template>
                </p-dialog>
                <p-dropdown
                  appPreventClearFilter
                  appendTo="body"
                  [options]="contactsVendors"
                  formControlName="vendorId"
                  optionLabel="name"
                  [showClear]="true"
                  optionValue="id"
                  [filter]="true"
                  (onChange)="setContactVendorSupplier($event)"
                  filterBy="name"
                  [virtualScroll]="true"
                  [itemSize]="30"
                  placeholder="Select vendor"
                  [optionDisabled]="isOptionDisabled.bind(this)"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.vendors, data: contactsVendors }"></ng-container>
                  </ng-template>
                  <ng-template let-item pTemplate="item">
                    <span [ngClass]="{'disabled-dropdown-item': item?.archived}">
                      {{ item?.name }}
                      <span *ngIf="item?.archived">{{ constants.archived }}</span>
                    </span>
                  </ng-template>
                </p-dropdown>
                <app-error-messages [control]="expensesFormGroup.controls.vendorId"></app-error-messages>
              </div>

              <div class="col-lg-3 col-md-6 col-12">
                <label class="required">Purchased By</label>
                <p-dropdown
                  appPreventClearFilter
                  appendTo="body"
                  [options]="purchaseByList"
                  formControlName="purchasedById"
                  optionLabel="name"
                  [showClear]="true"
                  optionValue="id"
                  [filter]="true"
                  filterBy="stockNumber"
                  [virtualScroll]="true"
                  [itemSize]="30"
                  placeholder="Select Purchased by"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.purchaseBy, data: purchaseByList }"></ng-container>
                  </ng-template>
                  <ng-template let-pipelineOwner pTemplate="item">
                    <span>{{ pipelineOwner.name }}</span>
                  </ng-template>
                </p-dropdown>
                <app-error-messages [control]="expensesFormGroup?.controls?.purchasedById"></app-error-messages>
              </div>
              <div class="row m-t-5">
                <div class="col-lg-6 col-md-6 col-12 position-relative">
                  <label [ngClass]="{ 'm-t-30': !financialInformation?.expense?.expensesAttachments?.length }">Description</label>
                  <textarea class="m-t-8" placeholder="Enter description" rows="6" formControlName="description"></textarea>
                  <app-error-messages [control]="expensesFormGroup.controls.description"></app-error-messages>
                </div>
                <ng-container *appHasPermission="[permissionActions.CREATE_FINANCIAL, permissionActions.UPDATE_FINANCIAL]">
                  <div *ngIf="showAttachmentsTab" class="col-lg-6 col-md-6 col-12">
                    <div class="d-flex space-between document-upload">
                      <div>
                        <label [ngClass]="{ 'm-t-20': !financialInformation?.expense?.expensesAttachments?.length }">Document</label>
                      </div>
                      <div class="d-flex justify-content-end">
                        <p-message severity="info" text="Max file size allowed is 10MB" styleClass="p-mr-2 file-size-message m-t-10"></p-message>
                      </div>
                    </div>
                    <div>
                      <section
                        *ngIf="!isViewMode && isPrimaryInventory"
                        class="m-t-10"
                        [ngClass]="{
                          'w-100': !fileUploadProgresses?.length && !financialInformation?.expense?.expensesAttachments?.length
                        }"
                      >
                        <div [ngClass]="{ files: fileUploadProgresses?.length || financialInformation?.expense?.expensesAttachments?.length }" class="drop-zone">
                          <span class="drop-zone__prompt">
                            <em class="pi pi-upload"></em>
                            <p class="title">Drop file to attach</p>
                            <p class="subtitle">or click to browse</p>
                          </span>
                          <input type="file" name="myFile" class="drop-zone__input cursor-pointer" #inputElement (change)="onFileSelect($event)" [accept]="constants.allowedPdfFormats" multiple />
                        </div>
                      </section>
                    </div>
                    <div>
                      <div *ngFor="let fileProgress of fileUploadProgresses; let fileIndex = index" class="files">
                        <div class="file-box-wrapper">
                          <div
                            class="file-box"
                            [ngClass]="{ 'in-progress': !fileProgress?.isResolved }"
                            [style.background-image]="'url(' + fileProgress.fileProperty?.fileUrl + ')'"
                          ></div>
                        </div>
                        <div class="d-flex space-between m-t-10">
                          <div>
                            <fa-icon class="text-danger" [icon]="faIcons.faFilePdf"></fa-icon>
                            <span class="file-name text-truncate m-l-10">{{ fileProgress?.file?.name }}</span>
                          </div>
                          <div class="d-flex justify-content-end">
                            <a (click)="utils.viewUploadedPdf(acquisitionFile)">
                              <em class="pi pi-eye download-icon"></em>
                            </a>
                            <a (click)="utils.downloadUploadedPdf(acquisitionFile)">
                              <em class="pi pi-download download-icon ms-2"></em>
                            </a>
                            <em
                              class="pi pi-trash text-danger ms-2 cursor-pointer"
                              *appHasPermission="[permissionActions.UPDATE_FINANCIAL]"
                              (click)="removeFileFromUpload(fileIndex)"
                            ></em>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="isEditMode">
                      <div *ngFor="let file of financialInformation?.expense?.expensesAttachments; let fileIndex = index" class="files">
                        <div class="file-box-wrapper">
                          <div class="file-box" [style.background-image]="'url(' + file?.fullExpensesAttachmentUrl + ')'"></div>
                        </div>
                        <div class="d-flex space-between m-t-10">
                          <div>
                            <fa-icon class="text-danger" [icon]="faIcons.faFilePdf"></fa-icon>
                            <span class="file-name text-truncate m-l-10">{{ getFileName(file?.url) }}</span>
                          </div>
                          <div class="d-flex justify-content-end">
                            <a [href]="file?.fullExpensesAttachmentUrl" target="_blank">
                              <em class="pi pi-eye download-icon"></em>
                            </a>
                            <em class="pi pi-download download-icon ms-2" (click)="downloadServerPDF(file)"></em>
                            <em *ngIf="!isViewMode" class="pi pi-trash text-danger ms-2 cursor-pointer" appShowLoaderOnApiCall (click)="onDeleteImage(file.id, $event)"></em>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
    <div class="col-12 m-t-20">
      <div class="d-flex space-between">
        <div class="d-flex title border-none">
          <h4>UNIT EXPENSES</h4>
          <span class="title border-none m-l-10 show-label">Stock# {{ activeAssociatedUnit?.stockNumber }} :</span>
          <span class="title border-none m-l-10 show-label">{{ activeAssociatedUnit?.yearMakeModel }}</span>
        </div>
        <div class="d-flex justify-content-end" *ngIf="!isViewMode">
          <button
            class="btn btn-primary left"
            [appImageIconSrc]="constants.staticImages.icons.addNew"
            (click)="openAndCloseModel('expense')"
            *appHasPermission="[permissionActions.CREATE_FINANCIAL]"
          >
            <span class="show-label">Add Expenses</span>
          </button>
        </div>
      </div>
      <div class="card tabs m-t-10">
        <div class="tab-content">
          <p-table
            class="no-column-selection"
            [value]="expenses"
            responsiveLayout="scroll"
            sortMode="single"
            [customSort]="true"
            [lazy]="true"
            [reorderableColumns]="true"
            (onLazyLoad)="onSortChange($event, getAll.bind(this))"
            [sortField]="'id'"
            [rowHover]="true"
            [loading]="isLoading"
          >
            <ng-template pTemplate="header" let-columns>
              <tr>
                <th scope="col">PO/RO</th>
                <th scope="col">Invoice</th>
                <th scope="col">Date</th>
                <th scope="col">Type</th>
                <th scope="col">Description</th>
                <th scope="col">Vendor</th>
                <th scope="col">Supplier</th>
                <th scope="col" style="width: 250px;">Document</th>
                <th scope="col">Amount</th>
                <ng-container *ngIf="!this.isViewMode && this.isPrimaryInventory">
                  <th scope="col" *appHasPermission="[permissionActions.UPDATE_FINANCIAL || permissionActions.DELETE_EXPENSES]" class="small-col text-center">Actions</th>
                </ng-container>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
              <tr>
                <td>{{ rowData?.poRoNumber }}</td>
                <td>{{ rowData?.invoiceNumber }}</td>
                <td>{{ rowData?.transDate | date: constants.dateFormat }}</td>
                <td>{{ rowData?.expenseType?.name }}</td>
                <td>{{ rowData?.description }}</td>
                <td>{{ rowData?.vendor?.name }}</td>
                <td>{{ rowData?.supplier?.name }}</td>
                <td class="text-truncate" style="max-width: 250px;">
                  <ng-container *ngIf="rowData?.expensesAttachments?.length > 0">
                    <ng-container *ngFor="let file of rowData?.expensesAttachments; let fileIndex = index">
                      <div class="my-2">
                        <a [href]="file?.fullExpensesAttachmentUrl" *ngIf="file?.fullExpensesAttachmentUrl"
                          target="_blank">
                          {{ getFileName(file?.url) }}
                        </a>
                      </div>
                    </ng-container>
                  </ng-container>
                </td>
                <td>${{ rowData?.amount }}</td>
                <ng-container *ngIf="!this.isViewMode && this.isPrimaryInventory">
                  <td class="actions" *appHasPermission="[permissionActions.UPDATE_FINANCIAL || permissionActions.DELETE_EXPENSES]">
                    <div class="actions-content">
                      <ng-container *appHasPermission="[permissionActions.UPDATE_FINANCIAL]">
                        <img class="m-l-10" [src]="constants.staticImages.icons.edit" (click)="onEdit(rowData)"
                          alt="Edit Expense" *ngIf="!isViewMode" />
                      </ng-container>
                      <ng-container *appHasPermission="[permissionActions.DELETE_EXPENSES]">
                        <img
                         class="m-l-10"
                          [src]="constants.staticImages.icons.deleteIcon"
                          *ngIf="!rowData?.firstExpense && !isViewMode"
                          (click)="onDelete(rowData, $event)"
                          appShowLoaderOnApiCall alt="Delete Expense"
                           />
                      </ng-container>
                    </div>
                  </td>
                </ng-container>
              </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
              <td [colSpan]="8" class="no-data">No data to display</td>
            </ng-template>
          </p-table>
          <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
        </div>
      </div>
    </div>
  </div>
</form>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateModal"
  position="right"
  (onHide)="modelPopups.showCreateModal = false"
  [showCloseIcon]="false"
  [fullScreen]="true"
  styleClass="p-sidebar-md"
  appendTo="body"
>
  <app-expenses-add
    (onClose)="onAddEditPopupClose(ModelType.EXPENSES)"
    [financialInformation]="financialInformation"
    [inventoryInfo]="inventoryInfo"
    *ngIf="modelPopups.showCreateModal"
    [expensesInfo]="selectedExpenses"
  >
  </app-expenses-add>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateVendor"
  position="right"
  (onHide)="modelPopups.showCreateVendor = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-vendor-add (onClose)="onAddEditPopupClose(ModelType.VENDOR)" *ngIf="modelPopups.showCreateVendor" [vendorInfo]="selectedVendor"> </app-vendor-add>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateContact"
  position="right"
  (onHide)="modelPopups.showCreateContact = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-contact-customer-add (onClose)="onAddEditPopupClose(ModelType.CONTACT)" *ngIf="modelPopups.showCreateContact"> </app-crm-contact-customer-add>
</p-sidebar>
<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateSupplier"
  position="right"
  (onHide)="modelPopups.showCreateSupplier = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-supplier-add (onClose)="onAddEditPopupClose(ModelType.SUPPLIER)" *ngIf="modelPopups.showCreateSupplier"> </app-supplier-add>
</p-sidebar>
